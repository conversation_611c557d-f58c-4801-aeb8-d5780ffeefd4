#!/usr/bin/env python3
"""
Battery Age Calculator

This script calculates the age of each battery that appears in the working vehicles and HV repair files.
The goal is to find the start date of each battery and calculate its age in days from today.

Logic:
1. Process HV repair data to find first appearance of batteries
2. Handle vehicle completeness scenarios:
   - Repair-only vehicles: use effective date as erstzulassung
   - Working+repair vehicles: use erstzulassung from working files
   - Working-only vehicles: assign battery start to erstzulassung
3. Always use the earliest possible start date for each battery
4. Generate comprehensive output with battery ages and statistics

Data Sources:
- working_vehicles.csv (matching vehicles)
- working_unique_vehicles.csv (unique vehicles)
- hv_repair_2025-06-02b.csv (repair events)
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
from typing import Dict, List, Tuple, Optional, Set
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("find_battery_age.log"),
    ],
)
logger = logging.getLogger(__name__)


class BatteryAgeCalculator:
    """Calculate battery ages based on repair events and vehicle data."""

    def __init__(self):
        self.today = datetime.now().date()

        # Data containers
        self.hv_repair_df = None
        self.working_vehicles_df = None
        self.working_unique_df = None

        # Processing results
        self.battery_start_dates = {}  # battery_id -> (start_date, vehicle)
        self.battery_vehicles = {}     # battery_id -> set of associated vehicles
        self.vehicle_info = {}         # vin -> vehicle info
        self.all_vehicles = set()      # all unique vehicles
        self.all_batteries = set()     # all unique batteries

        # Statistics
        self.stats = {
            'total_batteries': 0,
            'total_vehicles': 0,
            'repair_only_vehicles': 0,
            'working_repair_vehicles': 0,
            'working_only_vehicles': 0,
            'batteries_from_repair': 0,
            'batteries_from_working': 0,
            'start_date_updates': 0,
            'errors': []
        }

    def load_data(self):
        """Load all data files."""
        logger.info("Loading data files...")

        # Load HV repair data
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv('hv_repair_2025-06-02b.csv')
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")

        # Load working vehicles data
        logger.info("Loading working vehicles data...")
        self.working_vehicles_df = pd.read_csv('comparison_results/working_matching_vehicles.csv')
        self.working_unique_df = pd.read_csv('comparison_results/working_unique_vehicles.csv')
        logger.info(f"Loaded {len(self.working_vehicles_df)} matching vehicles")
        logger.info(f"Loaded {len(self.working_unique_df)} unique vehicles")

        # Combine working vehicles
        self.working_vehicles_df = pd.concat([self.working_vehicles_df, self.working_unique_df], ignore_index=True)
        logger.info(f"Total working vehicles: {len(self.working_vehicles_df)}")

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean HV repair data
        self.hv_repair_df['created'] = pd.to_datetime(self.hv_repair_df['created'], errors='coerce')
        self.hv_repair_df['battery_changed'] = self.hv_repair_df['battery_changed'].replace('--', None)
        self.hv_repair_df['battery_changed'] = pd.to_datetime(self.hv_repair_df['battery_changed'], errors='coerce')

        # Create effective date (battery_changed if available, otherwise created)
        self.hv_repair_df['effective_date'] = self.hv_repair_df['battery_changed'].fillna(self.hv_repair_df['created'])

        # Clean battery IDs
        for col in ['battery_id_old', 'battery_id_new']:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(['nan', '', ' ', 'None'], None)

        # Clean working vehicles data
        self.working_vehicles_df['erstzulassung'] = pd.to_datetime(self.working_vehicles_df['erstzulassung'], errors='coerce')

        for col in ['master', 'slave']:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(str)
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(['nan', '', ' ', 'None'], None)

        # Filter valid records
        self.hv_repair_df = self.hv_repair_df.dropna(subset=['vin', 'effective_date'])
        self.working_vehicles_df = self.working_vehicles_df.dropna(subset=['vin'])

        logger.info(f"After cleaning: {len(self.hv_repair_df)} repair records, {len(self.working_vehicles_df)} working vehicles")

    def process_hv_repair_data(self):
        """Process HV repair data to find battery first appearances."""
        logger.info("Processing HV repair data...")

        # Sort by effective date to process chronologically
        self.hv_repair_df = self.hv_repair_df.sort_values('effective_date')

        for _, row in self.hv_repair_df.iterrows():
            vin = row['vin']
            effective_date = row['effective_date'].date()
            old_battery = row['battery_id_old']
            new_battery = row['battery_id_new']

            # Process both old and new batteries - effective_date is first appearance for both
            for battery_id in [old_battery, new_battery]:
                if battery_id and pd.notna(battery_id):
                    self.all_batteries.add(battery_id)

                    # Initialize battery tracking
                    if battery_id not in self.battery_start_dates:
                        self.battery_start_dates[battery_id] = (effective_date, vin)
                        self.battery_vehicles[battery_id] = set()
                        logger.info(f"First appearance: Battery {battery_id} on {effective_date} in vehicle {vin}")
                    else:
                        # Check if this is an earlier date
                        current_date, current_vehicle = self.battery_start_dates[battery_id]
                        if effective_date < current_date:
                            logger.info(f"Updating start date for battery {battery_id}: {current_date} (vehicle {current_vehicle}) -> {effective_date} (vehicle {vin})")
                            self.battery_start_dates[battery_id] = (effective_date, vin)
                            self.stats['start_date_updates'] += 1

                    # Associate battery with vehicle
                    self.battery_vehicles[battery_id].add(vin)

            # Track vehicle
            self.all_vehicles.add(vin)

        self.stats['batteries_from_repair'] = len(self.battery_start_dates)
        logger.info(f"Found {len(self.battery_start_dates)} batteries from repair data")

    def build_vehicle_info(self):
        """Build vehicle information from working vehicles data."""
        logger.info("Building vehicle information...")

        for _, row in self.working_vehicles_df.iterrows():
            vin = row['vin']
            self.all_vehicles.add(vin)

            self.vehicle_info[vin] = {
                'vin': vin,
                'erstzulassung': row.get('erstzulassung'),
                'master_battery': row.get('master'),
                'slave_battery': row.get('slave'),
                'akz': row.get('akz'),
                'in_repair_data': vin in self.hv_repair_df['vin'].values
            }

        logger.info(f"Built info for {len(self.vehicle_info)} vehicles")

    def handle_vehicle_completeness(self):
        """Handle the three vehicle completeness scenarios."""
        logger.info("Handling vehicle completeness scenarios...")

        repair_vins = set(self.hv_repair_df['vin'].unique())
        working_vins = set(self.vehicle_info.keys())

        # Scenario 3a: Vehicles only in repair data
        repair_only_vins = repair_vins - working_vins
        for vin in repair_only_vins:
            self._handle_repair_only_vehicle(vin)

        # Scenario 3b: Vehicles in both repair and working data
        both_vins = repair_vins & working_vins
        for vin in both_vins:
            self._handle_working_repair_vehicle(vin)

        # Scenario 3c: Vehicles only in working data
        working_only_vins = working_vins - repair_vins
        for vin in working_only_vins:
            self._handle_working_only_vehicle(vin)

        self.stats['repair_only_vehicles'] = len(repair_only_vins)
        self.stats['working_repair_vehicles'] = len(both_vins)
        self.stats['working_only_vehicles'] = len(working_only_vins)

        logger.info(f"Processed vehicles: {len(repair_only_vins)} repair-only, {len(both_vins)} both, {len(working_only_vins)} working-only")

    def _handle_repair_only_vehicle(self, vin: str):
        """Handle vehicles that only appear in repair data."""
        # Find first repair event for this vehicle
        vehicle_repairs = self.hv_repair_df[self.hv_repair_df['vin'] == vin].sort_values('effective_date')
        if len(vehicle_repairs) == 0:
            return

        first_repair = vehicle_repairs.iloc[0]
        erstzulassung_date = first_repair['effective_date'].date()

        # The old battery from the first repair event was the original battery
        old_battery = first_repair['battery_id_old']
        if old_battery and pd.notna(old_battery):
            # Check if this battery's earliest start date is from this vehicle
            if old_battery in self.battery_start_dates:
                current_date, current_vehicle = self.battery_start_dates[old_battery]
                if current_vehicle == vin:
                    # Use erstzulassung as the start date for repair-only vehicles
                    logger.info(f"Using repair event date as erstzulassung for repair-only vehicle {vin}, battery {old_battery}")
                    # Keep the repair event date as erstzulassung for repair-only vehicles
            else:
                self._update_battery_start_date(old_battery, erstzulassung_date, f"Repair-only vehicle {vin} erstzulassung")

        # Also handle new battery if present
        new_battery = first_repair['battery_id_new']
        if new_battery and pd.notna(new_battery):
            self._update_battery_start_date(new_battery, erstzulassung_date, f"Repair-only vehicle {vin} first repair")

    def _handle_working_repair_vehicle(self, vin: str):
        """Handle vehicles that appear in both working and repair data."""
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info['erstzulassung']

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        erstzulassung_date = erstzulassung.date()

        # Find first repair event for this vehicle
        vehicle_repairs = self.hv_repair_df[self.hv_repair_df['vin'] == vin].sort_values('effective_date')
        if len(vehicle_repairs) > 0:
            first_repair = vehicle_repairs.iloc[0]

            # The old battery from the first repair was the original battery
            old_battery = first_repair['battery_id_old']
            if old_battery and pd.notna(old_battery):
                # Check if this battery's earliest start date is from this vehicle
                if old_battery in self.battery_start_dates:
                    current_date, current_vehicle = self.battery_start_dates[old_battery]
                    if current_vehicle == vin and erstzulassung_date < current_date:
                        logger.info(f"Updating battery {old_battery} start date from repair event {current_date} to erstzulassung {erstzulassung_date} for vehicle {vin}")
                        self.battery_start_dates[old_battery] = (erstzulassung_date, vin)
                        self.stats['start_date_updates'] += 1

        # Also handle batteries from working data
        for battery_field in ['master_battery', 'slave_battery']:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.all_batteries.add(battery_id)
                self._update_battery_start_date(battery_id, erstzulassung_date, f"Working vehicle {vin} {battery_field}")

    def _handle_working_only_vehicle(self, vin: str):
        """Handle vehicles that only appear in working data."""
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info['erstzulassung']

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        erstzulassung_date = erstzulassung.date()

        # Assign start date to batteries from working data
        for battery_field in ['master_battery', 'slave_battery']:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.all_batteries.add(battery_id)
                self._update_battery_start_date(battery_id, erstzulassung_date, f"Working-only vehicle {vin} {battery_field}")

    def _update_battery_start_date(self, battery_id: str, new_date: datetime.date, source: str):
        """Update battery start date, always using the earliest date."""
        # Extract vehicle from source string (assuming format like "Working vehicle {vin} master_battery")
        vin = source.split()[2] if len(source.split()) > 2 else "unknown"
        
        if battery_id not in self.battery_start_dates:
            self.battery_start_dates[battery_id] = (new_date, vin)
            self.battery_vehicles[battery_id] = set()
            logger.debug(f"New battery {battery_id}: start date {new_date} from {source}")
        else:
            current_date, current_vehicle = self.battery_start_dates[battery_id]
            if new_date < current_date:
                logger.info(f"Updating battery {battery_id}: {current_date} (vehicle {current_vehicle}) -> {new_date} (vehicle {vin}) from {source}")
                self.battery_start_dates[battery_id] = (new_date, vin)
                self.stats['start_date_updates'] += 1
            else:
                logger.debug(f"Keeping earlier date for battery {battery_id}: {current_date} (vehicle {current_vehicle}) vs {new_date} (vehicle {vin}) from {source}")

    def calculate_ages(self):
        """Calculate ages for all batteries."""
        logger.info("Calculating battery ages...")

        self.battery_ages = []

        for battery_id in self.all_batteries:
            if battery_id in self.battery_start_dates:
                start_date, start_vehicle = self.battery_start_dates[battery_id]
                age_days = (self.today - start_date).days

                self.battery_ages.append({
                    'battery_id': battery_id,
                    'start_date': start_date,
                    'start_vehicle': start_vehicle,
                    'age_in_days': age_days,
                    'note': f"Started in vehicle {start_vehicle}. Associated with vehicles: {', '.join(sorted(self.battery_vehicles.get(battery_id, set())))}"
                })
            else:
                self.battery_ages.append({
                    'battery_id': battery_id,
                    'start_date': None,
                    'start_vehicle': None,
                    'age_in_days': None,
                    'note': 'No start date found'
                })
                self.stats['errors'].append(f"No start date found for battery {battery_id}")

        self.stats['total_batteries'] = len(self.all_batteries)
        self.stats['total_vehicles'] = len(self.all_vehicles)

        logger.info(f"Calculated ages for {len(self.battery_ages)} batteries")

    def generate_outputs(self):
        """Generate output files."""
        logger.info("Generating output files...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate CSV output
        df = pd.DataFrame(self.battery_ages)
        csv_filename = f"battery_age_results_{timestamp}.csv"
        df.to_csv(csv_filename, index=False)
        logger.info(f"Saved results to {csv_filename}")

        # Generate statistics file
        stats_filename = f"battery_age_statistics_{timestamp}.txt"
        with open(stats_filename, 'w') as f:
            f.write("Battery Age Calculation Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {self.stats['total_batteries']}\n")
            f.write(f"Total Vehicles: {self.stats['total_vehicles']}\n")
            f.write(f"Repair-only Vehicles: {self.stats['repair_only_vehicles']}\n")
            f.write(f"Working+Repair Vehicles: {self.stats['working_repair_vehicles']}\n")
            f.write(f"Working-only Vehicles: {self.stats['working_only_vehicles']}\n")
            f.write(f"Batteries from Repair Data: {self.stats['batteries_from_repair']}\n")
            f.write(f"Start Date Updates: {self.stats['start_date_updates']}\n")
            f.write(f"Errors: {len(self.stats['errors'])}\n")

            if self.stats['errors']:
                f.write("\nErrors:\n")
                for error in self.stats['errors']:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        return csv_filename, stats_filename

    def run(self):
        """Run the complete battery age calculation process."""
        logger.info("Starting battery age calculation...")

        try:
            self.load_data()
            self.clean_data()
            self.process_hv_repair_data()
            self.build_vehicle_info()
            self.handle_vehicle_completeness()
            self.calculate_ages()
            csv_file, stats_file = self.generate_outputs()

            logger.info("Battery age calculation completed successfully!")
            logger.info(f"Results saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise


if __name__ == "__main__":
    calculator = BatteryAgeCalculator()
    calculator.run()
